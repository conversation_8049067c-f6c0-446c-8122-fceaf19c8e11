package services

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import models.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import utils.ConfigUtils
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

/**
 * User/Customer service implementation using SDK-style approach
 * Uses the same REST API as UserService but wrapped in SDK pattern for consistency
 * Maintains the same API interface as the original UserService
 */
class UserSDKService {
    private val logger: Logger = LoggerFactory.getLogger(UserSDKService::class.java)
    private val httpClient = HttpClient(CIO)

    private val merchantKeyId = ConfigUtils.getString("cybersource.merchantKeyId").also {
        require(it.isNotBlank()) { "cybersource.merchantKeyId must be configured" }
    }

    private val merchantSecretKey = ConfigUtils.getString("cybersource.secretKey").also {
        require(it.isNotBlank()) { "cybersource.secretKey must be configured" }
    }

    private val merchantId = ConfigUtils.getString("cybersource.merchantId").also {
        require(it.isNotBlank()) { "cybersource.merchantId must be configured" }
    }

    private val host = ConfigUtils.getString("cybersource.host")
        .removePrefix("https://")
        .removePrefix("http://")
        .also {
            require(it.isNotBlank()) { "cybersource.host must be configured" }
        }

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
    }

    init {
        logger.info("Initialized SDK User Service with merchantId: $merchantId and host: $host")
    }
    
    /**
     * Create a customer using SDK-style REST API approach
     */
    suspend fun createCustomer(request: UserCreateRequest): UserResponse {
        val resourcePath = "/tms/v2/customers"
        val url = "https://$host$resourcePath"

        return try {
            logger.info("Processing SDK customer creation request for customerId: ${request.buyerInfo.customerId}")
            logger.info("Email: ${request.buyerInfo.email}")

            val cyberSourceRequest = CyberSourceUserCreateRequest(
                buyerInformation = BuyerInformation(
                    merchantCustomerID = request.buyerInfo.customerId,
                    email = request.buyerInfo.email
                ),
                clientReferenceInformation = ClientReferenceInformation(
                    code = request.referenceInfo.code
                ),
                merchantDefinedInformation = request.customData?.map {
                    MerchantDefinedInformation(name = it.name, value = it.value)
                }
            )

            val requestBody = json.encodeToString(CyberSourceUserCreateRequest.serializer(), cyberSourceRequest)
            logger.info("SDK Customer creation request body prepared")

            val response = makeSignedRequest(
                url = url,
                httpMethod = "POST",
                resourcePath = resourcePath,
                requestBody = requestBody
            )

            val cyberSourceResponse = handleCyberSourceResponse(response)
            transformToSimpleResponse(cyberSourceResponse, "create")

        } catch (e: Exception) {
            logger.error("SDK Customer creation failed for customerId ${request.buyerInfo.customerId}. Error: ${e.message}", e)
            UserResponse(
                success = false,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_CREATION_FAILED",
                    message = "Failed to create customer: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }
    
    /**
     * Retrieve a customer using SDK-style REST API approach
     */
    suspend fun getCustomer(customerId: String): UserResponse {
        val resourcePath = "/tms/v2/customers/$customerId"
        val url = "https://$host$resourcePath"

        return try {
            logger.info("Processing SDK customer retrieval request for ID: $customerId")

            val response = makeSignedRequest(
                url = url,
                httpMethod = "GET",
                resourcePath = resourcePath,
                requestBody = ""
            )

            val cyberSourceResponse = handleCyberSourceResponse(response)
            transformToSimpleResponse(cyberSourceResponse, "get")

        } catch (e: Exception) {
            logger.error("SDK Customer retrieval failed for ID $customerId. Error: ${e.message}", e)
            UserResponse(
                success = false,
                userId = customerId,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_RETRIEVAL_FAILED",
                    message = "Failed to retrieve customer: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }
    
    /**
     * Delete a customer using SDK-style REST API approach
     */
    suspend fun deleteCustomer(customerId: String): UserResponse {
        val resourcePath = "/tms/v2/customers/$customerId"
        val url = "https://$host$resourcePath"

        return try {
            logger.info("Processing SDK customer deletion request for ID: $customerId")

            val response = makeSignedRequest(
                url = url,
                httpMethod = "DELETE",
                resourcePath = resourcePath,
                requestBody = ""
            )

            // For DELETE operations, a successful response typically has no body
            // Check the status code to determine success
            if (response.status.value in 200..299) {
                UserResponse(
                    success = true,
                    userId = customerId
                )
            } else {
                UserResponse(
                    success = false,
                    userId = customerId,
                    error = ErrorInfo(
                        code = "SDK_CUSTOMER_DELETION_FAILED",
                        message = "Failed to delete customer. Status: ${response.status}"
                    )
                )
            }

        } catch (e: Exception) {
            logger.error("SDK Customer deletion failed for ID $customerId. Error: ${e.message}", e)
            UserResponse(
                success = false,
                userId = customerId,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_DELETION_FAILED",
                    message = "Failed to delete customer: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }
    
    /**
     * Make a signed HTTP request to CyberSource API
     */
    private suspend fun makeSignedRequest(
        url: String,
        httpMethod: String,
        resourcePath: String,
        requestBody: String
    ): HttpResponse {
        val gmtDateTime = SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US).apply {
            timeZone = TimeZone.getTimeZone("GMT")
        }.format(Date())

        val digest = if (httpMethod != "GET" && httpMethod != "DELETE") {
            val sha256 = MessageDigest.getInstance("SHA-256")
            val hash = sha256.digest(requestBody.toByteArray())
            Base64.getEncoder().encodeToString(hash)
        } else ""

        val signatureString = buildString {
            if (httpMethod != "GET" && httpMethod != "DELETE") {
                append("digest: SHA-256=$digest\n")
            }
            append("host: $host\n")
            append("date: $gmtDateTime\n")
            append("(request-target): ${httpMethod.lowercase()} $resourcePath\n")
            append("v-c-merchant-id: $merchantId")
        }

        val decodedSecret = Base64.getDecoder().decode(merchantSecretKey)
        val mac = Mac.getInstance("HmacSHA256")
        mac.init(SecretKeySpec(decodedSecret, "HmacSHA256"))
        val signature = Base64.getEncoder().encodeToString(mac.doFinal(signatureString.toByteArray()))

        val signatureHeaderValue = buildString {
            append("keyid=\"$merchantKeyId\", ")
            append("algorithm=\"HmacSHA256\", ")
            if (httpMethod != "GET" && httpMethod != "DELETE") {
                append("headers=\"digest host date (request-target) v-c-merchant-id\", ")
            } else {
                append("headers=\"host date (request-target) v-c-merchant-id\", ")
            }
            append("signature=\"$signature\"")
        }

        return try {
            httpClient.request(url) {
                method = HttpMethod.parse(httpMethod)
                headers {
                    append("v-c-merchant-id", merchantId)
                    append("v-c-correlation-id", UUID.randomUUID().toString())
                    append("Date", gmtDateTime)
                    append("Host", host)
                    append("Signature", signatureHeaderValue)
                    if (httpMethod != "GET" && httpMethod != "DELETE") {
                        append("Digest", "SHA-256=$digest")
                        contentType(ContentType.Application.Json)
                    }

                    append("Accept", "application/json")
                    append("User-Agent", "Kotlin-CyberSource-SDK-Client/1.0")
                }
                if (httpMethod != "GET" && httpMethod != "DELETE") {
                    setBody(requestBody)
                }
            }
        } catch (e: Exception) {
            logger.error("Failed to execute CyberSource SDK customer request. Error: ${e.message}")
            throw e
        }
    }

    /**
     * Handle CyberSource API response
     */
    private suspend fun handleCyberSourceResponse(response: HttpResponse): CyberSourceUserResponse {
        val responseBody = response.bodyAsText()
        logger.info("SDK Customer API response status: ${response.status}")
        logger.debug("SDK Customer API response body: $responseBody")

        return when {
            response.status.value in 200..299 -> {
                try {
                    json.decodeFromString(CyberSourceUserResponse.serializer(), responseBody)
                } catch (e: SerializationException) {
                    logger.error("Failed to parse successful response", e)
                    throw IllegalStateException("Failed to parse API response: ${e.message}")
                }
            }
            else -> {
                logger.error("SDK Customer API request failed with status: ${response.status}")
                throw IllegalStateException("API request failed with status: ${response.status}")
            }
        }
    }

    /**
     * Transform CyberSource response to simple response format
     */
    private fun transformToSimpleResponse(
        cyberSourceResponse: CyberSourceUserResponse,
        operation: String
    ): UserResponse {
        val isSuccessful = cyberSourceResponse.id != null

        return UserResponse(
            success = isSuccessful,
            userId = cyberSourceResponse.id,
            user = if (isSuccessful && cyberSourceResponse.buyerInformation != null) {
                SimpleUserData(
                    id = cyberSourceResponse.id ?: "",
                    customerId = cyberSourceResponse.buyerInformation?.merchantCustomerID ?: "",
                    email = cyberSourceResponse.buyerInformation?.email ?: "",
                    referenceCode = cyberSourceResponse.clientReferenceInformation?.code,
                    customData = cyberSourceResponse.merchantDefinedInformation?.map {
                        CustomData(name = it.name, value = it.value)
                    },
                    createdAt = null // CyberSource doesn't return creation time in this format
                )
            } else null,
            error = if (!isSuccessful) {
                ErrorInfo(
                    code = "SDK_CUSTOMER_${operation.uppercase()}_FAILED",
                    message = "SDK Customer $operation operation failed"
                )
            } else null
        )
    }
}


package services

import Api.CustomerApi
import Invokers.ApiClient
import Invokers.ApiException
import Model.*
import com.cybersource.authsdk.core.MerchantConfig
import models.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import utils.ConfigUtils
import java.util.*

/**
 * User/Customer service implementation using Cybersource REST SDK
 * Maintains the same API interface as the original UserService
 * Uses the official Cybersource SDK for all operations
 */
class UserSDKService {
    private val logger: Logger = LoggerFactory.getLogger(UserSDKService::class.java)
    private val merchantProp: Properties
    private val apiClient: ApiClient
    private val customerApi: CustomerApi

    init {
        // Initialize merchant configuration from application config
        merchantProp = Properties().apply {
            setProperty("merchantID", ConfigUtils.getString("cybersource.merchantId"))
            setProperty("merchantKeyId", ConfigUtils.getString("cybersource.merchantKeyId"))
            setProperty("merchantsecretKey", ConfigUtils.getString("cybersource.secretKey"))
            setProperty("runEnvironment", if (ConfigUtils.getString("cybersource.host").contains("apitest")) "cybersource.sandbox" else "cybersource.production")
            setProperty("authenticationType", "http_signature")
            setProperty("enableLog", "true")
            setProperty("logDirectory", "logs")
            setProperty("logFileName", "cybs")
            setProperty("logFileMaxSize", "5242880")
        }

        // Initialize API client with merchant configuration
        apiClient = ApiClient()
        val merchantConfig = MerchantConfig(merchantProp)
        apiClient.merchantConfig = merchantConfig

        // Initialize Customer API
        customerApi = CustomerApi(apiClient)

        logger.info("Initialized Cybersource SDK User Service with merchantId: ${merchantProp.getProperty("merchantID")}")
    }

    /**
     * Create a customer using Cybersource SDK
     */
    suspend fun createCustomer(request: UserCreateRequest): UserResponse {
        return try {
            logger.info("Processing SDK customer creation request for customerId: ${request.buyerInfo.customerId}")
            logger.info("Email: ${request.buyerInfo.email}")

            // Build the SDK request object
            val postCustomerRequest = PostCustomerRequest().apply {
                // Set buyer information
                buyerInformation = Tmsv2customersBuyerInformation().apply {
                    merchantCustomerID(request.buyerInfo.customerId)
                    email(request.buyerInfo.email)
                }

                // Set client reference information
                clientReferenceInformation = Tmsv2customersClientReferenceInformation().apply {
                    code(request.referenceInfo.code)
                }

                // Set merchant defined information if provided
                request.customData?.let { customDataList ->
                    val merchantDefinedInfo = customDataList.map { customData ->
                        Tmsv2customersMerchantDefinedInformation().apply {
                            name(customData.name)
                            value(customData.value)
                        }
                    }
                    merchantDefinedInformation = merchantDefinedInfo
                }
            }

            // Make the API call using SDK
            val result = customerApi.postCustomer(postCustomerRequest, null)

            // Get response details
            val responseCode = apiClient.responseCode
            val status = apiClient.status

            logger.info("SDK Customer creation response - Code: $responseCode, Status: $status")
            logger.debug("SDK Customer creation result: $result")

            // Transform SDK response to our standard format
            transformSdkCreateResponseToUserResponse(result, responseCode, status, "create")

        } catch (e: ApiException) {
            logger.error("SDK Customer creation failed for customerId ${request.buyerInfo.customerId}. API Error: ${e.message}", e)
            UserResponse(
                success = false,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_CREATION_FAILED",
                    message = "Failed to create customer: ${e.message ?: "API Error"}"
                )
            )
        } catch (e: Exception) {
            logger.error("SDK Customer creation failed for customerId ${request.buyerInfo.customerId}. Error: ${e.message}", e)
            UserResponse(
                success = false,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_CREATION_FAILED",
                    message = "Failed to create customer: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }

    /**
     * Retrieve a customer using Cybersource SDK
     */
    suspend fun getCustomer(customerId: String): UserResponse {
        return try {
            logger.info("Processing SDK customer retrieval request for ID: $customerId")

            // Make the API call using SDK
            val result = customerApi.getCustomer(customerId, null)

            // Get response details
            val responseCode = apiClient.responseCode
            val status = apiClient.status

            logger.info("SDK Customer retrieval response - Code: $responseCode, Status: $status")
            logger.debug("SDK Customer retrieval result: $result")

            // Transform SDK response to our standard format
            transformSdkGetResponseToUserResponse(result, responseCode, status, "get")

        } catch (e: ApiException) {
            logger.error("SDK Customer retrieval failed for ID $customerId. API Error: ${e.message}", e)
            UserResponse(
                success = false,
                userId = customerId,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_RETRIEVAL_FAILED",
                    message = "Failed to retrieve customer: ${e.message ?: "API Error"}"
                )
            )
        } catch (e: Exception) {
            logger.error("SDK Customer retrieval failed for ID $customerId. Error: ${e.message}", e)
            UserResponse(
                success = false,
                userId = customerId,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_RETRIEVAL_FAILED",
                    message = "Failed to retrieve customer: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }

    /**
     * Delete a customer using Cybersource SDK
     */
    suspend fun deleteCustomer(customerId: String): UserResponse {
        return try {
            logger.info("Processing SDK customer deletion request for ID: $customerId")

            // Make the API call using SDK
            customerApi.deleteCustomer(customerId, null)

            // Get response details
            val responseCode = apiClient.responseCode
            val status = apiClient.status

            logger.info("SDK Customer deletion response - Code: $responseCode, Status: $status")

            // For DELETE operations, check response code for success
            if (responseCode?.toIntOrNull() in 200..299) {
                UserResponse(
                    success = true,
                    userId = customerId
                )
            } else {
                UserResponse(
                    success = false,
                    userId = customerId,
                    error = ErrorInfo(
                        code = "SDK_CUSTOMER_DELETION_FAILED",
                        message = "Failed to delete customer. Response code: $responseCode, Status: $status"
                    )
                )
            }

        } catch (e: ApiException) {
            logger.error("SDK Customer deletion failed for ID $customerId. API Error: ${e.message}", e)
            UserResponse(
                success = false,
                userId = customerId,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_DELETION_FAILED",
                    message = "Failed to delete customer: ${e.message ?: "API Error"}"
                )
            )
        } catch (e: Exception) {
            logger.error("SDK Customer deletion failed for ID $customerId. Error: ${e.message}", e)
            UserResponse(
                success = false,
                userId = customerId,
                error = ErrorInfo(
                    code = "SDK_CUSTOMER_DELETION_FAILED",
                    message = "Failed to delete customer: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }

    /**
     * Transform Cybersource SDK create response to our standard UserResponse format
     */
    private fun transformSdkCreateResponseToUserResponse(
        sdkResponse: TmsV2CustomersResponse?,
        responseCode: String?,
        status: String?,
        operation: String
    ): UserResponse {
        val isSuccessful = responseCode?.toIntOrNull() in 200..299 && sdkResponse?.id != null

        return UserResponse(
            success = isSuccessful,
            userId = sdkResponse?.id,
            user = if (isSuccessful && sdkResponse != null) {
                SimpleUserData(
                    id = sdkResponse.id ?: "",
                    customerId = sdkResponse.buyerInformation?.merchantCustomerID ?: "",
                    email = sdkResponse.buyerInformation?.email ?: "",
                    referenceCode = sdkResponse.clientReferenceInformation?.code,
                    customData = sdkResponse.merchantDefinedInformation?.map { merchantInfo ->
                        CustomData(name = merchantInfo.name ?: "", value = merchantInfo.value ?: "")
                    },
                    createdAt = null // CyberSource doesn't return creation time in this format
                )
            } else null,
            error = if (!isSuccessful) {
                ErrorInfo(
                    code = "SDK_CUSTOMER_${operation.uppercase()}_FAILED",
                    message = "SDK Customer $operation operation failed. Response code: $responseCode, Status: $status"
                )
            } else null
        )
    }

    /**
     * Transform Cybersource SDK get response to our standard UserResponse format
     */
    private fun transformSdkGetResponseToUserResponse(
        sdkResponse: TmsV2CustomersResponse?,
        responseCode: String?,
        status: String?,
        operation: String
    ): UserResponse {
        val isSuccessful = responseCode?.toIntOrNull() in 200..299 && sdkResponse?.id != null

        return UserResponse(
            success = isSuccessful,
            userId = sdkResponse?.id,
            user = if (isSuccessful && sdkResponse != null) {
                SimpleUserData(
                    id = sdkResponse.id ?: "",
                    customerId = sdkResponse.buyerInformation?.merchantCustomerID ?: "",
                    email = sdkResponse.buyerInformation?.email ?: "",
                    referenceCode = sdkResponse.clientReferenceInformation?.code,
                    customData = sdkResponse.merchantDefinedInformation?.map { merchantInfo ->
                        CustomData(name = merchantInfo.name ?: "", value = merchantInfo.value ?: "")
                    },
                    createdAt = null // CyberSource doesn't return creation time in this format
                )
            } else null,
            error = if (!isSuccessful) {
                ErrorInfo(
                    code = "SDK_CUSTOMER_${operation.uppercase()}_FAILED",
                    message = "SDK Customer $operation operation failed. Response code: $responseCode, Status: $status"
                )
            } else null
        )
    }
}


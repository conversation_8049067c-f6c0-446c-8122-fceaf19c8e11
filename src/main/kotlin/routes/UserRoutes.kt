package routes

import io.ktor.http.*
import io.ktor.serialization.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import models.*
import org.slf4j.LoggerFactory
import services.UserSDKService

fun Route.userRoutes() {
    val userSDKService = UserSDKService()

    route("/api/v1") {
        route("/users") {
             // Create customer endpoint
            post("/create") {
                val logger = LoggerFactory.getLogger("UserRoutes.Create")

                try {
                    val request = call.receive<UserCreateRequest>()
                    validateCreateRequest(request)

                    logger.info("Processing customer creation request for customerId: ${request.buyerInfo.customerId}")

                    val response = userSDKService.createCustomer(request)

                    if (response.success) {
                        logger.info("Customer creation successful. Customer ID: ${response.userId}")
                        call.respond(HttpStatusCode.Created, response)
                    } else {
                        logger.warn("Customer creation failed. Reason: ${response.error?.message}")
                        call.respond(HttpStatusCode.BadRequest, response)
                    }

                } catch (e: ContentTransformationException) {
                    logger.error("Invalid customer creation request format", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Expected: {\"buyerInfo\": {\"customerId\": \"string\", \"email\": \"string\"}, \"referenceInfo\": {\"code\": \"string\"}}"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Error in customer creation", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "CUSTOMER_CREATION_ERROR",
                                message = "Failed to create customer: ${e.message}"
                            )
                        )
                    )
                }
            }

            // Retrieve customer endpoint
            post("/retrieve") {
                val logger = LoggerFactory.getLogger("UserRoutes.Retrieve")

                try {
                    val request = call.receive<UserRetrieveRequest>()

                    if (request.userId.isBlank()) {
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = UserResponse(
                                success = false,
                                error = ErrorInfo(
                                    code = "MISSING_USER_ID",
                                    message = "User ID is required"
                                )
                            )
                        )
                        return@post
                    }

                    logger.info("Processing customer retrieval request for ID: ${request.userId}")

                    val response = userSDKService.getCustomer(request.userId)

                    if (response.success) {
                        logger.info("Customer retrieval successful for ID: ${request.userId}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("Customer retrieval failed for ID: ${request.userId}. Reason: ${response.error?.message}")
                        val statusCode = if (response.error?.code == "RESOURCE_NOT_FOUND") {
                            HttpStatusCode.NotFound
                        } else {
                            HttpStatusCode.BadRequest
                        }
                        call.respond(statusCode, response)
                    }

                } catch (e: Exception) {
                    logger.error("Error in customer retrieval", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "CUSTOMER_RETRIEVAL_ERROR",
                                message = "Failed to retrieve customer: ${e.message}"
                            )
                        )
                    )
                }
            }

            // Delete customer endpoint
            post("/delete") {
                val logger = LoggerFactory.getLogger("UserRoutes.Delete")

                try {
                    val request = call.receive<UserDeleteRequest>()

                    if (request.userId.isBlank()) {
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = UserResponse(
                                success = false,
                                error = ErrorInfo(
                                    code = "MISSING_USER_ID",
                                    message = "User ID is required"
                                )
                            )
                        )
                        return@post
                    }

                    logger.info("Processing customer deletion request for ID: ${request.userId}")

                    val response = userSDKService.deleteCustomer(request.userId)

                    if (response.success) {
                        logger.info("Customer deletion successful for ID: ${request.userId}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("Customer deletion failed for ID: ${request.userId}. Reason: ${response.error?.message}")
                        val statusCode = if (response.error?.code == "RESOURCE_NOT_FOUND") {
                            HttpStatusCode.NotFound
                        } else {
                            HttpStatusCode.BadRequest
                        }
                        call.respond(statusCode, response)
                    }

                } catch (e: Exception) {
                    logger.error("Error in customer deletion", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "CUSTOMER_DELETION_ERROR",
                                message = "Failed to delete customer: ${e.message}"
                            )
                        )
                    )
                }
            }
        }
    }
}

private fun validateCreateRequest(request: UserCreateRequest) {
    require(request.buyerInfo.customerId.isNotBlank()) { "Customer ID is required" }
    require(request.buyerInfo.email.isNotBlank()) { "Email is required" }
    require(request.referenceInfo.code.isNotBlank()) { "Reference code is required" }

    // Validate email format
    require(request.buyerInfo.email.contains("@") && request.buyerInfo.email.contains(".")) {
        "Invalid email format"
    }
}
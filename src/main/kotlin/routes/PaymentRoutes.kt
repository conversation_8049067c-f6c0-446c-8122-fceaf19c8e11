package routes

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import models.*
import org.slf4j.LoggerFactory
import services.CybersourceSDKService

fun Route.paymentRoutes() {
    val cybersourceSDKService by lazy {
        CybersourceSDKService()
    }

    route("/api/v1") {
        route("/payments") {

            // Payment initialization endpoint - routes to SDK authorize
            post("/init") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Init")

                try {
                    val request = call.receive<PaymentInitRequest>()
                    validateInitRequest(request)

                    logger.info("Processing payment init request for reference: ${request.orderDetails.referenceCode}")
                    logger.info("Payment method: ${request.paymentMethod.type}, Platform: ${request.platform.source}")

                    when (request.paymentMethod.type) {
                        "credit-card" -> {
                            // Convert to authorization request and process with SDK
                            val authRequest = PaymentAuthorizationRequest(
                                paymentMethod = request.paymentMethod,
                                platform = request.platform,
                                orderDetails = request.orderDetails,
                                cardDetails = request.cardDetails ?: throw IllegalArgumentException("Card details are required for credit card payments"),
                                billingAddress = request.billingAddress,
                                customerInfo = request.customerInfo
                            )

                            val response = cybersourceSDKService.authorize(authRequest)
                            call.respond(response.toPaymentInitResponse())
                        }
                        "paypal" -> {
                            call.respond(
                                HttpStatusCode.NotImplemented,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "NOT_IMPLEMENTED",
                                        message = "PayPal payment method is not yet implemented"
                                    )
                                )
                            )
                        }
                        "apple-pay" -> {
                            call.respond(
                                HttpStatusCode.NotImplemented,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "NOT_IMPLEMENTED",
                                        message = "Apple Pay payment method is not yet implemented"
                                    )
                                )
                            )
                        }
                        "google-pay" -> {
                            call.respond(
                                HttpStatusCode.NotImplemented,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "NOT_IMPLEMENTED",
                                        message = "Google Pay payment method is not yet implemented"
                                    )
                                )
                            )
                        }
                        else -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "UNSUPPORTED_PAYMENT_METHOD",
                                        message = "Unsupported payment method: ${request.paymentMethod.type}"
                                    )
                                )
                            )
                        }
                    }
                } catch (e: ContentTransformationException) {
                    logger.error("Invalid payment init request format", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentInitResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Please check your JSON structure and required fields."
                            )
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    logger.error("Validation error in payment init request", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentInitResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "VALIDATION_ERROR",
                                message = e.message ?: "Request validation failed"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Unexpected error processing payment init", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentInitResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            // Authorization endpoint
            post("/authorize") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Authorize")

                try {
                    val request = call.receive<PaymentAuthorizationRequest>()
                    validateAuthorizationRequest(request)

                    logger.info("Processing authorization request for reference: ${request.orderDetails.referenceCode}")

                    val response = cybersourceSDKService.authorize(request)

                    if (response.success) {
                        logger.info("Authorization successful for reference: ${request.orderDetails.referenceCode}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("Authorization failed for reference: ${request.orderDetails.referenceCode}. Reason: ${response.error?.message}")
                        call.respond(HttpStatusCode.BadRequest, response)
                    }

                } catch (e: ContentTransformationException) {
                    logger.error("Invalid authorization request format", e)
                    call.respond<PaymentResponse>(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Please check your JSON structure and required fields."
                            )
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    logger.error("Validation error in authorization request", e)
                    call.respond<PaymentResponse>(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "VALIDATION_ERROR",
                                message = e.message ?: "Request validation failed"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Unexpected error in authorization", e)
                    call.respond<PaymentResponse>(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            // Approve endpoint (capture) - uses SDK
            post("/approve") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Approve")

                try {
                    val request = call.receive<PaymentApprovalRequest>()
                    validateApprovalRequest(request)

                    logger.info("Processing approval request for payment ID: ${request.paymentId}")

                    val response = cybersourceSDKService.approve(request)

                    if (response.success) {
                        logger.info("Approval successful for payment ID: ${request.paymentId}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("Approval failed for payment ID: ${request.paymentId}. Reason: ${response.error?.message}")
                        call.respond(HttpStatusCode.BadRequest, response)
                    }

                } catch (e: ContentTransformationException) {
                    logger.error("Invalid approval request format", e)
                    call.respond<PaymentResponse>(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Please check your JSON structure and required fields."
                            )
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    logger.error("Validation error in approval request", e)
                    call.respond<PaymentResponse>(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "VALIDATION_ERROR",
                                message = e.message ?: "Request validation failed"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Unexpected error processing approval", e)
                    call.respond<PaymentResponse>(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            // Reverse endpoint
            post("/reverse") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Reversal")

                try {
                    val reversalRequest = call.receive<ReversalRequest>()
                    val paymentId = reversalRequest.reversalInformation.paymentId

                    if (paymentId.isBlank()) {
                        call.respond<PaymentResponse>(
                            status = HttpStatusCode.BadRequest,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "MISSING_PAYMENT_ID",
                                    message = "Payment ID is required"
                                )
                            )
                        )
                        return@post
                    }

                    logger.info("Processing reversal request for payment ID: $paymentId")

                    val response = cybersourceSDKService.reversePayment(reversalRequest)
                    call.respond(HttpStatusCode.OK, response)

                } catch (e: ContentTransformationException) {
                    logger.error("Invalid reversal request format", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Please check your JSON structure and required fields."
                            )
                        )
                    )
                } catch (e: Exception) {
                    val paymentId = try {
                        call.receive<ReversalRequest>().reversalInformation.paymentId
                    } catch (ex: Exception) {
                        "unknown"
                    }
                    logger.error("Reversal failed for ID $paymentId", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            paymentId = paymentId,
                            error = ErrorInfo(
                                code = "REVERSAL_FAILED",
                                message = "Failed to reverse payment"
                            )
                        )
                    )
                }
            }
        }
    }
}


// Extension functions and validation functions moved outside the paymentRoutes() function

private fun PaymentResponse.toPaymentInitResponse(): PaymentInitResponse {
    return PaymentInitResponse(
        success = this.success,
        paymentId = this.paymentId,
        status = this.status,
        referenceCode = this.referenceCode,
        transactionDetails = this.transactionDetails,
        approvalInfo = this.approvalInfo,
        error = this.error,
        metadata = this.metadata
    )
}

private fun validateInitRequest(request: PaymentInitRequest) {
    // Validate payment method
    require(request.paymentMethod.type.isNotBlank()) { "Payment method type is required" }
    require(request.paymentMethod.type in listOf("credit-card", "paypal", "apple-pay", "google-pay")) {
        "Invalid payment method type. Supported types: credit-card, paypal, apple-pay, google-pay"
    }

    // Validate platform
    require(request.platform.source.isNotBlank()) { "Platform source is required" }
    require(request.platform.source in listOf("web", "mobile-app", "api")) {
        "Invalid platform source. Supported sources: web, mobile-app, api"
    }

    // Validate order details
    require(request.orderDetails.amount.isNotBlank()) { "Order amount is required" }
    require(request.orderDetails.currency.isNotBlank()) { "Order currency is required" }

    // Validate card details if paymentN method is credit-card
    if (request.paymentMethod.type == "credit-card") {
        require(request.cardDetails != null) { "Card details are required for credit card payments" }
        validateCardDetails(request.cardDetails)
    }

    // Validate billing address
    require(request.billingAddress.firstName.isNotBlank()) { "Billing first name is required" }
    require(request.billingAddress.lastName.isNotBlank()) { "Billing last name is required" }
    require(request.billingAddress.address1.isNotBlank()) { "Billing address is required" }
    require(request.billingAddress.city.isNotBlank()) { "Billing city is required" }
    require(request.billingAddress.state.isNotBlank()) { "Billing state is required" }
    require(request.billingAddress.postalCode.isNotBlank()) { "Billing postal code is required" }
    require(request.billingAddress.country.isNotBlank()) { "Billing country is required" }

    // Validate customer info
    require(request.customerInfo.email.isNotBlank()) { "Customer email is required" }
    require(request.customerInfo.phoneNumber.isNotBlank()) { "Customer phone number is required" }

    // Validate amount format
    require(request.orderDetails.amount.matches(Regex("\\d+\\.\\d{2}"))) {
        "Invalid amount format. Use decimal format like 102.21"
    }

    // Validate currency format
    require(request.orderDetails.currency.matches(Regex("[A-Z]{3}"))) {
        "Invalid currency format. Use 3-letter currency code like USD, EUR, etc."
    }

    // Validate email format
    require(request.customerInfo.email.contains("@") && request.customerInfo.email.contains(".")) {
        "Invalid email format"
    }
}

private fun validateCardDetails(cardDetails: CardDetails) {
    require(cardDetails.number.isNotBlank()) { "Card number is required" }
    require(cardDetails.expirationMonth.isNotBlank()) { "Card expiration month is required" }
    require(cardDetails.expirationYear.isNotBlank()) { "Card expiration year is required" }

    // Validate card number format
    require(cardDetails.number.replace(" ", "").matches(Regex("\\d{13,19}"))) {
        "Invalid card number format"
    }

    // Validate expiration month
    require(cardDetails.expirationMonth.matches(Regex("(0[1-9]|1[0-2])"))) {
        "Invalid expiration month. Use MM format (01-12)"
    }

    // Validate expiration year
    require(cardDetails.expirationYear.matches(Regex("\\d{4}")) &&
            cardDetails.expirationYear.toInt() >= 2024) {
        "Invalid expiration year. Use YYYY format and ensure it's not expired"
    }
}

private fun validateAuthorizationRequest(request: PaymentAuthorizationRequest) {
    // Validate payment method
    require(request.paymentMethod.type.isNotBlank()) { "Payment method type is required" }
    require(request.paymentMethod.type in listOf("credit-card", "debit-card", "digital-wallet")) {
        "Invalid payment method type. Supported types: credit-card, debit-card, digital-wallet"
    }

    // Validate platform
    require(request.platform.source.isNotBlank()) { "Platform source is required" }
    require(request.platform.source in listOf("web", "mobile-app", "api")) {
        "Invalid platform source. Supported sources: web, mobile-app, api"
    }

    // Validate order details
    require(request.orderDetails.amount.isNotBlank()) { "Order amount is required" }
    require(request.orderDetails.currency.isNotBlank()) { "Order currency is required" }

    // Validate card details (only for card payments)
    if (request.paymentMethod.type in listOf("credit-card", "debit-card")) {
        require(request.cardDetails.number.isNotBlank()) { "Card number is required for card payments" }
        require(request.cardDetails.expirationMonth.isNotBlank()) { "Card expiration month is required" }
        require(request.cardDetails.expirationYear.isNotBlank()) { "Card expiration year is required" }

        // Validate card number format
        require(request.cardDetails.number.replace(" ", "").matches(Regex("\\d{13,19}"))) {
            "Invalid card number format"
        }

        // Validate expiration month
        require(request.cardDetails.expirationMonth.matches(Regex("(0[1-9]|1[0-2])"))) {
            "Invalid expiration month. Use MM format (01-12)"
        }

        // Validate expiration year
        require(request.cardDetails.expirationYear.matches(Regex("\\d{4}")) &&
                request.cardDetails.expirationYear.toInt() >= 2024) {
            "Invalid expiration year. Use YYYY format and ensure it's not expired"
        }
    }

    // Validate billing address
    require(request.billingAddress.firstName.isNotBlank()) { "Billing first name is required" }
    require(request.billingAddress.lastName.isNotBlank()) { "Billing last name is required" }
    require(request.billingAddress.address1.isNotBlank()) { "Billing address is required" }
    require(request.billingAddress.city.isNotBlank()) { "Billing city is required" }
    require(request.billingAddress.state.isNotBlank()) { "Billing state is required" }
    require(request.billingAddress.postalCode.isNotBlank()) { "Billing postal code is required" }
    require(request.billingAddress.country.isNotBlank()) { "Billing country is required" }

    // Validate customer info
    require(request.customerInfo.email.isNotBlank()) { "Customer email is required" }
    require(request.customerInfo.phoneNumber.isNotBlank()) { "Customer phone number is required" }

    // Validate amount format
    require(request.orderDetails.amount.matches(Regex("\\d+\\.\\d{2}"))) {
        "Invalid amount format. Use decimal format like 102.21"
    }

    // Validate currency format
    require(request.orderDetails.currency.matches(Regex("[A-Z]{3}"))) {
        "Invalid currency format. Use 3-letter currency code like USD, EUR, etc."
    }

    // Validate email format
    require(request.customerInfo.email.contains("@") && request.customerInfo.email.contains(".")) {
        "Invalid email format"
    }
}

private fun validateApprovalRequest(request: PaymentApprovalRequest) {
    require(request.paymentId.isNotBlank()) { "Payment ID is required" }
    require(request.orderDetails.amount.isNotBlank()) { "Amount is required" }
    require(request.orderDetails.currency.isNotBlank()) { "Currency is required" }

    // Validate amount format
    require(request.orderDetails.amount.matches(Regex("\\d+\\.\\d{2}"))) {
        "Invalid amount format. Use decimal format like 102.21"
    }

    // Validate currency
    require(request.orderDetails.currency.matches(Regex("[A-Z]{3}"))) {
        "Invalid currency format. Use 3-letter currency code like USD, EUR, etc."
    }
}
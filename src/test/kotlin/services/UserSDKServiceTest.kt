package services

import kotlinx.coroutines.runBlocking
import models.*
import org.junit.Test
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class UserSDKServiceTest {

    @Test
    fun testUserSDKServiceInitialization() {
        // This test verifies that the UserSDKService can be initialized
        // without throwing exceptions (assuming proper configuration)
        try {
            val userSDKService = UserSDKService()
            assertNotNull(userSDKService)
            println("UserSDKService initialized successfully")
        } catch (e: Exception) {
            println("UserSDKService initialization failed (expected if config is missing): ${e.message}")
            // This is expected to fail in test environment without proper config
            assertTrue(e.message?.contains("must be configured") == true)
        }
    }

    @Test
    fun testUserCreateRequestModel() {
        // Test that our models are properly structured
        val request = UserCreateRequest(
            buyerInfo = BuyerInfo(
                customerId = "test-customer-123",
                email = "<EMAIL>"
            ),
            referenceInfo = ReferenceInfo(
                code = "TEST-REF-001"
            ),
            customData = listOf(
                CustomData(name = "data1", value = "test data")
            )
        )

        assertNotNull(request)
        assertTrue(request.buyerInfo.customerId == "test-customer-123")
        assertTrue(request.buyerInfo.email == "<EMAIL>")
        assertTrue(request.referenceInfo.code == "TEST-REF-001")
        assertTrue(request.customData?.size == 1)
        println("UserCreateRequest model test passed")
    }
}

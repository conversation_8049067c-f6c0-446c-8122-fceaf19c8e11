# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# VS Code
.vscode/

# Environment Variables & Config
*.env
.env
application-local.conf
application-prod.conf
local.properties

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# Kotlin
*.kotlin_module

# Sensitive files
**/resources/application.conf
**/resources/application.conf.backup
**/resources/cybs.properties
keys/
*.p12
*.pem
*.key

# CyberSource sensitive files
cybs.properties

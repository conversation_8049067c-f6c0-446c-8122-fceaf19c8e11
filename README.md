# Payment API Documentation

A comprehensive payment processing API that supports credit card transactions with initialization, approval, and reversal capabilities, plus user/customer management.

## Base URL
```
http://127.0.0.1:8080/api/v1
```

## API Endpoints

### Payment Endpoints

### 1. Initialize Payment

Initialize a payment transaction with the specified payment method.

**Endpoint:** `POST /payments/init`

**Description:** Creates a new payment transaction and authorizes the payment method. Users must specify the payment method type (e.g., credit-card).

#### Request Body

```json
{
    "paymentMethod": {
        "type": "credit-card"
    },
    "platform": {
        "source": "web"
    },
    "orderDetails": {
        "referenceCode": "ORDER-2025-007",
        "amount": "300.00",
        "currency": "USD"
    },
    "cardDetails": {
        "number": "****************",
        "expirationMonth": "12",
        "expirationYear": "2025",
        "securityCode": "123"
    },
    "billingAddress": {
        "firstName": "<PERSON>",
        "lastName": "Doe",
        "address1": "123 Main Street",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "country": "US"
    },
    "customerInfo": {
        "email": "<EMAIL>",
        "phoneNumber": "******-123-4567"
    }
}
```

#### Response

```json
{
    "success": true,
    "paymentId": "7544224898576367204805",
    "status": "AUTHORIZED",
    "referenceCode": "ORDER-2025-007",
    "transactionDetails": {
        "amount": "300.00",
        "currency": "USD",
        "approvalCode": "831000",
        "transactionId": "MCC053823",
        "processorResponseCode": "000"
    },
    "approvalInfo": {
        "canApprove": true,
        "approvalAmount": "300.00"
    },
    "metadata": {
        "submitTime": "2025-08-05T19:34:50Z",
        "cardType": "002"
    }
}
```

### 2. Approve Payment

Approve a previously authorized payment transaction.

**Endpoint:** `POST /payments/approve`

**Description:** Processes the final approval of an authorized payment transaction.

#### Request Body

```json
{
  "paymentId": "7546610062536917404806",
  "orderDetails": {
    "amount": "300.00",
    "currency": "USD"
  }
}
```

#### Response

```json
{
    "success": true,
    "paymentId": "7544225538586531004807",
    "status": "PENDING",
    "referenceCode": "7544224898576367204805",
    "transactionDetails": {
        "currency": "USD"
    },
    "metadata": {
        "submitTime": "2025-08-05T19:35:53Z"
    }
}
```

### 3. Reverse Payment

Reverse or refund a completed payment transaction.

**Endpoint:** `POST /payments/reverse`

**Description:** Initiates a reversal of a previously processed payment transaction.

#### Request Body

```json
{
  "reversalInformation": {
    "paymentId": "7546606788956173004807",
    "amountDetails": {
      "totalAmount": "300.00",
      "currency": "USD"
    },
    "reason": "testing"
  }
}
```

#### Response

```json
{
    "success": true,
    "paymentId": "7544219011206334104806",
    "status": "FAILED",
    "referenceCode": "ORDER-2025-004",
    "transactionDetails": {
        "currency": "USD",
        "processorResponseCode": "000"
    },
    "metadata": {
        "submitTime": "2025-08-05T19:25:01Z"
    }
}
```

## User Management Endpoints

### 4. Create User/Customer

Create a new customer in the system.

**Endpoint:** `POST /users/create`

**Description:** Creates a new customer with buyer information and reference details.

#### Request Body

```json
{
    "buyerInfo": {
        "customerId": "CUST-2025-001",
        "email": "<EMAIL>"
    },
    "referenceInfo": {
        "code": "REF-2025-001"
    },
    "customData": [
        {
            "name": "department",
            "value": "sales"
        },
        {
            "name": "region",
            "value": "north-america"
        }
    ]
}
```

#### Response

```json
{
    "success": true,
    "userId": "7544224898576367204805",
    "user": {
        "customerId": "CUST-2025-001",
        "email": "<EMAIL>",
        "status": "ACTIVE"
    }
}
```

### 5. Retrieve User/Customer

Retrieve an existing customer by ID.

**Endpoint:** `POST /users/retrieve`

**Description:** Retrieves customer information using the customer ID.

#### Request Body

```json
{
    "userId": "7544224898576367204805"
}
```

#### Response

```json
{
    "success": true,
    "userId": "7544224898576367204805",
    "user": {
        "customerId": "CUST-2025-001",
        "email": "<EMAIL>",
        "status": "ACTIVE",
        "createdDate": "2025-01-15T10:30:00Z"
    }
}
```

### 6. Delete User/Customer

Delete an existing customer from the system.

**Endpoint:** `POST /users/delete`

**Description:** Removes a customer from the system using the customer ID.

#### Request Body

```json
{
    "userId": "7544224898576367204805"
}
```

#### Response

```json
{
    "success": true,
    "userId": "7544224898576367204805"
}
```

## API Flows

### Payment Flow

1. **Initialize Payment** - Create and authorize a payment transaction
2. **Approve Payment** - Complete the payment processing
3. **Reverse Payment** - Refund or reverse a transaction (if needed)

### User Management Flow

1. **Create User** - Register a new customer in the system
2. **Retrieve User** - Get customer information for existing users
3. **Delete User** - Remove a customer from the system

## Payment Method Types

Currently supported payment methods:
- `credit-card` - Credit card payments

## Status Codes

### Payment Status Codes
- `AUTHORIZED` - Payment has been authorized but not yet captured
- `PENDING` - Payment is being processed
- `FAILED` - Payment processing failed
- `COMPLETED` - Payment successfully processed
- `REVERSED` - Payment has been reversed/refunded

### User Status Codes
- `ACTIVE` - User account is active
- `INACTIVE` - User account is inactive
- `DELETED` - User account has been deleted

## Request Fields

### Payment Request Fields

### Payment Method
- `type` (string, required) - The payment method type (e.g., "credit-card")

### Platform
- `source` (string) - The platform source (e.g., "web", "mobile")

### Order Details
- `referenceCode` (string, required) - Unique order reference
- `amount` (string, required) - Transaction amount
- `currency` (string, required) - Currency code (e.g., "USD")

### Card Details (for credit-card payments)
- `number` (string, required) - Credit card number
- `expirationMonth` (string, required) - Card expiration month
- `expirationYear` (string, required) - Card expiration year
- `securityCode` (string, required) - Card security code (CVV)

### Billing Address
- `firstName` (string, required) - Cardholder first name
- `lastName` (string, required) - Cardholder last name
- `address1` (string, required) - Primary address line
- `city` (string, required) - City
- `state` (string, required) - State/Province
- `postalCode` (string, required) - Postal/ZIP code
- `country` (string, required) - Country code

### Customer Information
- `email` (string, required) - Customer email address
- `phoneNumber` (string) - Customer phone number

### User Management Request Fields

### Buyer Information
- `customerId` (string, required) - Unique customer identifier
- `email` (string, required) - Customer email address

### Reference Information
- `code` (string, required) - Reference code for the customer

### Custom Data (optional)
- `name` (string) - Custom field name
- `value` (string) - Custom field value

### User Operations
- `userId` (string, required) - User ID for retrieve/delete operations

## Error Handling

All API responses include a `success` boolean field. When `success` is `false`, additional error information will be provided in the response.

### Common Error Codes

#### Payment Errors
- `PAYMENT_PROCESSING_ERROR` - General payment processing failure
- `INVALID_CARD_DETAILS` - Invalid credit card information
- `INSUFFICIENT_FUNDS` - Insufficient funds for transaction

#### User Management Errors
- `CUSTOMER_CREATION_ERROR` - Failed to create customer
- `CUSTOMER_RETRIEVAL_ERROR` - Failed to retrieve customer
- `CUSTOMER_DELETION_ERROR` - Failed to delete customer
- `INVALID_REQUEST_FORMAT` - Invalid request format
- `VALIDATION_ERROR` - Request validation failed
- `MISSING_USER_ID` - User ID is required
- `RESOURCE_NOT_FOUND` - Customer not found

## Authentication

All API endpoints require Basic Authentication. Include the following header in your requests:

```
Authorization: Basic <base64-encoded-credentials>
```

Where `<base64-encoded-credentials>` is the base64 encoding of `username:password`.

## Security Notes

- All API calls should be made over HTTPS in production
- Credit card numbers and sensitive data should be handled according to PCI DSS compliance standards
- Store payment IDs and user IDs securely for transaction tracking and future operations
- Customer data should be handled according to privacy regulations (GDPR, CCPA, etc.)

## Testing

Use the provided sample requests with test credit card numbers for development and testing purposes. The example uses a test Mastercard number (****************).

### Test Customer Data
- Customer ID: `TEST-CUST-001`
- Email: `<EMAIL>`
- Reference Code: `TEST-REF-001`